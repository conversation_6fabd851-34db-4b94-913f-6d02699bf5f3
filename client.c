#include <sys/types.h>
#include <sys/socket.h>
#include<pthread.h>
#include <netinet/in.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
 
#define TEST_FILE  "./test.wav"

enum 
{
	UDP_MSG_AUDIO_OPEN_TALK = 20001,
	UDP_MSG_AUDIO_CLOSE_TALK,		//20002
	UDP_MSG_AUDIO_TALK_FRAME,		//20003
	UDP_MSG_AUDIO_TALK_OTHER,		//20004
};


#pragma pack(1)
typedef struct tagUdpAudioMsgHead
{
    uint32_t id;
    char magic[5];
    uint32_t dataLen;
    char data[960];
	char userName[64];
	char password[64];
} UdpAudioMsgHead, PUdpAudioMsgHead;
#pragma pack()
int main(int argc, char **argv)
{
	printf("This is a UDP client\n");
    char sendBuff[1400] = {0};
    char *pSendBuff = sendBuff;
	char buffer[960] = {0};

    struct sockaddr_in addr;
    int sock;
	FILE *fp = NULL;
    int readBytes = 0;

	PUdpAudioMsgHead *pMsg = (PUdpAudioMsgHead *)pSendBuff;
	pMsg->id = UDP_MSG_AUDIO_OPEN_TALK;
	memcpy(pMsg->magic, "EZLO",sizeof("EZLO") - 1);
	memcpy(pMsg->userName, "admin", sizeof("admin") - 1);
	memcpy(pMsg->password, "A0E75C21042F", sizeof("A0E75C21042F") - 1);	
	printf("%d,%d,%d\n",sizeof("EZLO"),sizeof("admin"),sizeof("A0E75C21042F"));
	//sleep(5);
	fp = fopen(TEST_FILE,"rb");
	if(NULL == fp)
	{
		printf("fopen err!\n");
		return -1;
	}
	
    if ( (sock=socket(AF_INET, SOCK_DGRAM, 0)) <0)
    {
        perror("socket");
        exit(1);
    }
    addr.sin_family = AF_INET;
    addr.sin_port = htons(49587);
    addr.sin_addr.s_addr = inet_addr("************");
    if (addr.sin_addr.s_addr == INADDR_NONE)
    {
        printf("Incorrect ip address!");
        close(sock);
        exit(1);
    }
    int hasReadWavLen = 0;
	int ret = 0;
	ret = sendto(sock, pSendBuff, sizeof(PUdpAudioMsgHead) , 0, (struct sockaddr *)&addr, sizeof(addr))	;
    while( (fp != NULL) && (!feof(fp)))
    {
	    memset(buffer, 0x00 , sizeof(buffer));		
		readBytes = fread(buffer, 1, 960,fp);
		if(readBytes < 0)
		{
			printf("fread error!\n");
			break;
		}
		else
		{	
			hasReadWavLen += readBytes;
		}
		if(readBytes != 960)
		{
			break;
		}
		else
		{
			pMsg->dataLen = sizeof(PUdpAudioMsgHead);
			printf("magic:%s,id:%d,name:%s,pwd:%s,len:%d\n",pMsg->magic,pMsg->id,pMsg->userName,pMsg->password,sizeof(PUdpAudioMsgHead));
			memset(pMsg->data,0x00,readBytes);
			pMsg->id = UDP_MSG_AUDIO_TALK_FRAME;
			memcpy(pMsg->data, buffer, readBytes);		
			ret = sendto(sock, pSendBuff, sizeof(PUdpAudioMsgHead) , 0, (struct sockaddr *)&addr, sizeof(addr))	;
            printf("ret:%d\n",ret);			
			if( ret < 0)
			{
				perror("sendto");
				close(sock);
				break;
			}
			else
			{
				//printf("22 ret:%d\n",ret);
			}
		}
		//usleep(60*1000);
 
    }
	pMsg->id = UDP_MSG_AUDIO_CLOSE_TALK;
    sendto(sock, pSendBuff, sizeof(PUdpAudioMsgHead) , 0, (struct sockaddr *)&addr, sizeof(addr))	;
    return 0;
}
